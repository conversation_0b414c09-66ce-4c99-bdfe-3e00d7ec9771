# 主题适配文档

### 项目结构

```
src/style/
├── index.scss              # 样式入口文件
├── variables/               # 主题变量目录
│   ├── colorMode.scss      # 基础颜色变量定义（从Figma导出）
│   ├── light.scss          # 浅色主题CSS变量映射
│   ├── dark.scss           # 暗黑主题CSS变量映射
│   └── number.scss         # 数值变量（尺寸、间距等）
├── variable.scss           # 项目布局变量
└── element.scss            # Element Plus组件样式覆盖
```

### 实现方式

**1. 核心原理**

使用纯CSS变量系统，通过DOM类名（`.light`、`.dark`）控制主题切换。变量直接从Figma设计稿导出，确保设计与开发的一致性。

**2. 主题切换逻辑**

- 通过DOM类名（`.light`、`.dark`）控制主题
- 使用 `document.startViewTransition` API实现平滑过渡动画

## 主题配置指南

### 1. UnoCSS集成

**uno.config.ts**

```typescript
export function unocssPlugin() {
  return Unocss({
    theme: {
      colors: {
        primary: 'var(--el-color-primary)',
        success: 'var(--el-color-success)',
        text: {
          primary: 'var(--el-text-color-primary)',
          regular: 'var(--el-text-color-regular)',
          DEFAULT: 'var(--el-text-color-primary)',
        },
        border: {
          light: 'var(--el-border-color-light)',
          DEFAULT: 'var(--el-border-color)',
        },
      },
    },
  })
}
```
## 变量使用指南

### 1. 一般用法

```scss
.custom-component {
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
  border: 1px solid var(--el-border-color);
}

.design-token-example {
  background: var(--Light-Primary-Primary-9);
  padding: var(--Space-Large-16);
  border-radius: var(--el-border-radius-base);
}
```

### 2. 在UnoCSS中使用

```html
<!-- 使用css变量 -->
<div class="bg-$el-border-color gap-$gap">
  测试测试
</div>

<div class="bg-[--el-border-color] gap-[--gap]">
  测试测试
</div>

<div class="text-[var(--el-border-color)] gap-[var(--gap)]">
  测试测试
</div>

<!-- 使用配置好的颜色类 -->
<div class="bg-border-color leading-base" text="primary base">
  主色背景白色文字
</div>
```

## 参考资源

- [View Transitions API](https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API)
- [ElementPlus主题切换-掘金](https://juejin.cn/post/7444109900878151731?searchId=20250722103341ED8796E1D95A8958F9A7)
- [VueUse、View Transitions Api主题切换-掘金](https://juejin.cn/post/7326707110212485130)
- [UnoCSS主题配置](https://unocss.dev/config/theme)
