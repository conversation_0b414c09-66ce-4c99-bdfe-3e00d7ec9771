import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { initMinio, Minio, putObject } from 'minio-js'
import request from '@/utils/request.ts'

/**
 * 获取凭证
 */
function getUploadToken() {
  return request<any, MinioData>({
    url: '/api/v1/files/getCredentials',
    method: 'get',
  })
}

export const FileAPI = {
  minioConfig: {},
  bucket: null,
  minioClient: Minio,

  /**
   * 初始化minio配置信息，获取凭证，进入页面后调用(如获取用户信息时)
   */
  async init() {
    return getUploadToken().then((res) => {
      const { port, host, credentials, bucketName } = res
      const endPoint = host.split('//')[1]
      const useSSL = host.startsWith('https')
      this.minioConfig = {
        endPoint,
        port: Number(port),
        useSSL,
        accessKey: credentials.accessKey,
        secretKey: credentials.secretKey,
        sessionToken: credentials.sessionToken,
      }
      this.bucket = bucketName
      this.minioClient = new Minio.Client(this.minioConfig)
    })
  },

  /**
   * 上传文件
   * @param file
   * @param dir 上传目录名
   */
  upload: (file: File, dir: string): Promise<FileInfo> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file)
      reader.addEventListener('load', (e) => {
        const res = e.target.result
        initMinio(FileAPI.minioConfig)
        const objectName = `${dir}/${dayjs().format('YYYY-MM-DD')}/${encodeURIComponent(file.name)}`
        // 上传
        putObject(FileAPI.bucket, res, objectName, (err) => {
          if (err) {
            ElMessage.error('上传失败')
            reject(err)
          }
          else {
            getFileUrl(objectName).then((url) => {
              resolve({ url, name: file.name, objectName })
            })
          }
        })
      })
    })
  },

  /**
   * 移除文件
   */
  remove: (objectName: string) => {
    return new Promise((resolve, reject) => {
      FileAPI.minioClient
        .removeObject(FileAPI.bucket, objectName)
        .then(() => {
          resolve(null)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
}

/**
 * 私有桶获取可访问url
 * @param objectName 文件objectName
 * @param expiryInSeconds 过期时间（秒），默认一天有效期
 */
export function getFileUrl(objectName: string, expiryInSeconds = 24 * 60 * 60): Promise<string> {
  return FileAPI.minioClient.presignedGetObject(FileAPI.bucket, objectName, expiryInSeconds)
}

export interface FileInfo {
  name: string
  url: string
  objectName?: string
}

export interface MinioConfig {
  endPoint: string
  port: number
  useSSL: boolean
  accessKey: string
  secretKey: string
  sessionToken: string
}

export interface MinioData {
  host: string
  port: string
  bucketName: string
  credentials: MinioDataCredentials
}
export interface MinioDataCredentialsExpiration {}
export interface MinioDataCredentials {
  accessKey: string
  secretKey: string
  sessionToken: string
  expiration: MinioDataCredentialsExpiration
  expired: boolean
}
