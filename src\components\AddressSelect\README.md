# AddressSelect 地址选择组件

一个基于 [Leaflet](https://leafletjs.cn/reference.html) 地图和高德瓦片图(可自由更换)的地址选择器组件，支持地址搜索、地图点击选择和详情查看功能。

## 功能特性

- 🗺️ 基于 Leaflet 和高德地图瓦片服务
- 🔍 支持关键词搜索地址
- 📍 支持地图点击选择位置
- 🎯 支持省市筛选
- 📖 支持只读详情模式

## 组件接口

### Props

| 参数     | 类型    | 默认值 | 说明                                   |
| -------- | ------- | ------ | -------------------------------------- |
| editable | boolean | true   | 是否为编辑模式，false 时为详情查看模式 |

### Events

| 事件名 | 参数                                                       | 说明               |
| ------ | ---------------------------------------------------------- | ------------------ |
| change | `{ latitude: number, longitude: number, address: string }` | 地址选择变化时触发 |

### v-model

| 名称      | 类型        | 说明           |
| --------- | ----------- | -------------- |
| (default) | boolean     | 对话框显示状态 |
| data      | AddressInfo | 地址信息数据   |

## 数据结构

### AddressInfo

```typescript
interface AddressInfo {
  latitude: number // 纬度
  longitude: number // 经度
  address: string // 地址描述
}
```

## 使用示例

### 基本用法

```vue
<script setup>
const visible = ref(false)
const addressData = ref({
  latitude: 30.505399066242695,
  longitude: 103.62545013427736,
  address: '四川省成都市大邑县安仁古镇',
})

function handleAddressChange(data) {
  console.log('地址变化:', data)
}
</script>

<template>
  <div>
    <el-button @click="visible = true">选择地址</el-button>

    <AddressSelect
      v-model="visible"
      v-model:data="addressData"
      @change="handleAddressChange"
    />
  </div>
</template>
```

### 只读模式

```vue
<script setup>
const visible = ref(false)
const addressData = ref({
  latitude: 39.9042,
  longitude: 116.4074,
  address: '北京市朝阳区',
})
</script>

<template>
  <AddressSelect
    v-model="visible"
    v-model:data="addressData"
    :editable="false"
  />
</template>
```
