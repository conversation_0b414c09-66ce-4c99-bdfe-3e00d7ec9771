<!-- 横向滚动组件，支持点击按钮滚动、拖拽滚动、滚轮滚动 -->
<script lang="ts" setup>
import { useScroll } from '@/hooks/useScroll'

defineOptions({
  inheritAttrs: false,
})

const { showArrow = true } = defineProps<{ showArrow?: boolean }>()

const scrollbarRef = ref()
const { scrollToLeft, scrollToRight, refresh, arrivedState } = useScroll(scrollbarRef)

onMounted(() => {
  // 监听元素尺寸变化，变化后执行refresh
  const resizeObserver = new ResizeObserver(refresh)
  // 观察一个或多个元素
  resizeObserver.observe(scrollbarRef.value)
})

defineExpose({
  refresh,
})
</script>

<template>
  <div class="horizontalScroll relative">
    <div
      v-if="showArrow && !arrivedState.left"
      class="arrow-shadow arrow-left absolute z-1 -left-14 top-1/2 -translate-1/2 bg-background grid place-items-center w-38 h-38 rounded-1/2 shrink-0 cursor-pointer"
      hover="opacity-70"
      @click="scrollToLeft"
    >
      <img src="@/assets/images/<EMAIL>" class="w-30 h-30" alt="" draggable="false" />
    </div>
    <!--      切换按钮 -->
    <div v-bind="$attrs" ref="scrollbarRef" class="select-none overflow-x-auto">
      <slot />
    </div>
    <div
      v-if="showArrow && !arrivedState.right"
      class="arrow-shadow arrow-right absolute z-1 -right-39 top-1/2 -translate-y-1/2 bg-background grid place-items-center w-38 h-38 rounded-1/2 shrink-0 cursor-pointer"
      hover="opacity-70"
      @click="scrollToRight"
    >
      <img src="@/assets/images/<EMAIL>" class="w-30 h-30 rotate-180" alt="" draggable="false" />
    </div>
  </div>
</template>

<style scoped lang="scss">
//重置滚动条高度
::-webkit-scrollbar {
  height: 0;
}
</style>
