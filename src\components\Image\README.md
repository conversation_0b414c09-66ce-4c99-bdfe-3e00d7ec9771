# Image 图片组件

## 概述

`Image` 组件是基于 Element Plus 的 `el-image` 组件封装的图片展示组件，主要功能是自动将图片`ObjectName`转换为可访问的 URL 地址。

## 特性

- 📦 **完整继承**：继承 Element Plus Image 组件的所有属性，用法一致
- 🖼️ **图片预览**：支持点击图片进行大图预览
- 🔄 **自动转换 URL**：自动将文件`ObjectName`转换为可访问的 URL

## 默认添加了以下属性：

- `preview-src-list`：默认当前图片是可预览的，如不想预览可传空数组 `[]`
- `fit="cover"`：图片适应方式，默认为 cover
- `preview-teleported`：插入至 body 元素上，默认为 true
- `hide-on-click-modal`：点击遮罩层是否关闭预览弹窗，默认为 true

```html
<el-image
  v-if="addressableSrc"
  fit="cover"
  v-bind="$attrs"
  :src="addressableSrc"
  preview-teleported
  hide-on-click-modal
  :preview-src-list="addressablePreviewSrcList"
/>

```

## 基本用法

```vue
<template>
  <Image src="file_object_name" class="w-50 h-50 rounded-4" />
</template>
```

### 详细用法参考 [Element Plus Image 组件文档](https://element-plus.org/zh-CN/component/image)
