<script setup lang="ts">
import type { LocationQueryRaw, RouteRecordRaw } from 'vue-router'
import { Bottom, Clock, Close, Delete, Search, Top } from '@element-plus/icons-vue'
import { useEventListener } from '@vueuse/core'
import router from '@/router'
import { usePermissionStore } from '@/store/permisson.ts'

const HISTORY_KEY = 'menu_search_history'
const MAX_HISTORY = 5

const isModalVisible = ref(false)

interface SearchItem {
  title: string
  path: string
  name?: string
  icon?: string
  redirect?: string
  params?: LocationQueryRaw
}

/**
 * 高性能搜索索引管理器
 */
class MenuSearchIndex {
  private charIndex = new Map<string, Set<SearchItem>>() // 字符级索引
  private keywords = new Map<SearchItem, string[]>() // 预处理关键词

  constructor(items: SearchItem[]) {
    this.buildIndex(items)
  }

  private buildIndex(items: SearchItem[]) {
    this.charIndex.clear()
    this.keywords.clear()

    for (const item of items) {
      const title = item.title.toLowerCase()

      // 预处理关键词（标题分词）
      const keywords = this.extractKeywords(title)
      this.keywords.set(item, keywords)

      // 建立字符级索引
      for (const char of title) {
        if (!this.charIndex.has(char)) {
          this.charIndex.set(char, new Set())
        }

        this.charIndex.get(char)!.add(item)
      }
    }
  }

  private extractKeywords(title: string): string[] {
    // 提取关键词：完整标题 + 每个字符
    const keywords = [title]
    for (const element of title) {
      keywords.push(element)
    }

    return keywords
  }

  /**
   * 高性能模糊搜索
   */
  search(keyword: string, maxResults = 50): SearchItem[] {
    if (!keyword || keyword.trim() === '') {
      return []
    }

    const searchTerm = keyword.toLowerCase().trim()

    // 使用字符索引优化搜索
    const firstChar = searchTerm[0]
    const candidates = this.charIndex.get(firstChar)

    if (!candidates || candidates.size === 0) {
      return []
    }

    // 在候选集中进行相关性匹配
    const results: Array<{ item: SearchItem, score: number }> = []

    for (const item of candidates) {
      const score = this.calculateRelevanceScore(item.title.toLowerCase(), searchTerm)
      if (score > 0) {
        results.push({ item, score })
      }
    }

    // 按相关性排序并返回
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults)
      .map(r => r.item)
  }

  private calculateRelevanceScore(title: string, searchTerm: string): number {
    // 完全匹配
    if (title === searchTerm)
      return 100

    // 开头匹配
    if (title.startsWith(searchTerm))
      return 80

    // 完整包含
    if (title.includes(searchTerm))
      return 60

    // 字符序列匹配
    let matchCount = 0
    let searchIndex = 0

    for (let i = 0; i < title.length && searchIndex < searchTerm.length; i++) {
      if (title[i] === searchTerm[searchIndex]) {
        matchCount++
        searchIndex++
      }
    }

    const matchRatio = matchCount / searchTerm.length
    return matchRatio >= 0.6 ? Math.floor(matchRatio * 40) : 0
  }
}

// 从本地存储加载搜索历史
const searchHistory = ref<SearchItem[]>([])
function loadSearchHistory() {
  const historyStr = localStorage.getItem(HISTORY_KEY)
  if (historyStr) {
    try {
      searchHistory.value = JSON.parse(historyStr)
    }
    catch {
      searchHistory.value = []
    }
  }
}

loadSearchHistory()

// 保存搜索历史到本地存储
function saveSearchHistory() {
  localStorage.setItem(HISTORY_KEY, JSON.stringify(searchHistory.value))
}

// 添加项目到搜索历史
function addToHistory(item: SearchItem) {
  const index = searchHistory.value.findIndex(i => i.path === item.path)

  if (index !== -1) {
    searchHistory.value.splice(index, 1)
  }

  searchHistory.value.unshift(item)

  if (searchHistory.value.length > MAX_HISTORY) {
    searchHistory.value = searchHistory.value.slice(0, MAX_HISTORY)
  }

  saveSearchHistory()
}

// 移除历史记录项
function removeHistoryItem(index: number) {
  searchHistory.value.splice(index, 1)
  saveSearchHistory()
}

// 清空历史记录
function clearHistory() {
  searchHistory.value = []
  localStorage.removeItem(HISTORY_KEY)
}

// 注册全局快捷键
function handleKeyDown(e: KeyboardEvent) {
  if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'k') {
    e.preventDefault()
    openSearchModal()
  }
}

const permissionStore = usePermissionStore()
useEventListener('keydown', handleKeyDown)

// 打开搜索模态框
const searchKeyword = ref('')
const searchInputRef = ref()
const activeIndex = ref(-1)
function openSearchModal() {
  searchKeyword.value = ''
  activeIndex.value = -1
  isModalVisible.value = true
  setTimeout(() => {
    searchInputRef.value.focus()
  }, 100)
}

// 关闭搜索模态框
function closeSearchModal() {
  isModalVisible.value = false
}

// 菜单项和搜索结果
const menuItems = ref<SearchItem[]>([])
const searchResults = ref<SearchItem[]>([])

// 更新搜索结果 - 使用高性能索引
function updateSearchResults() {
  activeIndex.value = -1

  if (searchKeyword.value && searchIndex) {
    // 使用优化的搜索索引
    searchResults.value = searchIndex.search(searchKeyword.value, 20)
  }
  else {
    searchResults.value = []
  }
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  updateSearchResults()
})

// 显示搜索结果
const displayResults = computed(() => searchResults.value)

// 执行搜索
function selectActiveResult() {
  if (displayResults.value.length > 0 && activeIndex.value >= 0) {
    navigateToRoute(displayResults.value[activeIndex.value])
  }
}

// 导航搜索结果
function navigateResults(direction: string) {
  if (displayResults.value.length === 0)
    return

  if (direction === 'up') {
    activeIndex.value = activeIndex.value <= 0
      ? displayResults.value.length - 1
      : activeIndex.value - 1
  }
  else if (direction === 'down') {
    activeIndex.value = activeIndex.value >= displayResults.value.length - 1
      ? 0
      : activeIndex.value + 1
  }
}

// 跳转到路由
function navigateToRoute(item: SearchItem) {
  closeSearchModal()
  addToHistory(item)
  router.push({ path: item.path, query: item.params })
}

// 加载路由并构建搜索索引
const excludedRoutes = ref(['/redirect', '/login', '/401', '/404'])

function loadRoutes(routes: RouteRecordRaw[], parentPath = '') {
  for (const route of routes) {
    const path = route.path.startsWith('/')
      ? route.path
      : `${parentPath}${parentPath.endsWith('/') ? '' : '/'}${route.path}`

    if (excludedRoutes.value.includes(route.path))
      continue

    // 添加当前路由（如果有标题）
    if (route.meta?.title) {
      const title = route.meta.title === 'dashboard' ? '首页' : route.meta.title

      // 确定跳转路径：优先使用redirect，否则使用第一个子路由，最后使用当前路径
      let targetPath = path
      if (route.redirect && typeof route.redirect === 'string') {
        targetPath = route.redirect
      }
      else if (route.children && route.children.length > 0) {
        // 寻找第一个有效的子路由

        const firstValidChild = findFirstValidChild(route.children, path)
        if (firstValidChild) {
          targetPath = firstValidChild
        }
      }

      menuItems.value.push({
        title,
        path: targetPath,
        name: typeof route.name === 'string' ? route.name : undefined,
        icon: route.meta.icon,
        redirect: typeof route.redirect === 'string' ? route.redirect : undefined,
        params: route.meta.params
          ? structuredClone(toRaw(route.meta.params))
          : undefined,
      })
    }

    // 递归处理子路由
    if (route.children) {
      loadRoutes(route.children, path)
    }
  }
}

// 查找第一个有效的子路由
function findFirstValidChild(children: RouteRecordRaw[], parentPath: string): string | null {
  for (const child of children) {
    const childPath = child.path.startsWith('/')
      ? child.path
      : `${parentPath}${parentPath.endsWith('/') ? '' : '/'}${child.path}`

    if (excludedRoutes.value.includes(child.path))
      continue

    // 如果子路由有标题且没有重定向，说明是可访问的页面
    if (child.meta?.title && !child.redirect) {
      return childPath
    }

    // 如果子路由还有子路由，递归查找
    if (child.children) {
      const result = findFirstValidChild(child.children, childPath)
      if (result)
        return result
    }
  }

  return null
}

// 初始化
loadRoutes(permissionStore.routes)
let searchIndex = new MenuSearchIndex(menuItems.value)
</script>

<template>
  <div @click="openSearchModal">
    <el-icon class="cursor-pointer"><Search /></el-icon>
    <el-dialog
      v-model="isModalVisible"
      width="30%"
      append-to-body
      :show-close="false"
      @close="closeSearchModal"
    >
      <template #header>
        <el-input
          ref="searchInputRef"
          v-model="searchKeyword"
          size="large"
          placeholder="输入菜单名称关键字搜索"
          clearable
          @keyup.enter="selectActiveResult"
          @keydown.up.prevent="navigateResults('up')"
          @keydown.down.prevent="navigateResults('down')"
          @keydown.esc="closeSearchModal"
        >
          <template #prepend>
            <el-button :icon="Search" />
          </template>
        </el-input>
      </template>

      <div class="search-result">
        <!-- 搜索历史 -->
        <template v-if="searchKeyword === '' && searchHistory.length > 0">
          <div class="search-history">
            <div class="search-history__title">
              搜索历史
              <el-button
                type="primary"
                text
                size="small"
                class="search-history__clear"
                @click="clearHistory"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <ul class="search-history__list">
              <li
                v-for="(item, index) in searchHistory"
                :key="index"
                class="search-history__item"
                @click="navigateToRoute(item)"
              >
                <div class="search-history__icon">
                  <el-icon><Clock /></el-icon>
                </div>
                <span class="search-history__name">{{ item.title }}</span>
                <div class="search-history__action">
                  <el-icon @click.stop="removeHistoryItem(index)"><Close /></el-icon>
                </div>
              </li>
            </ul>
          </div>
        </template>

        <!-- 搜索结果 -->
        <template v-else>
          <ul v-if="displayResults.length > 0">
            <li
              v-for="(item, index) in displayResults"
              :key="item.path"
              class="search-result__item"
              :class="{ 'search-result__item--active': index === activeIndex }"
              @click="navigateToRoute(item)"
            >
              <div v-if="item.icon" :class="`i-sidebar-${item.icon}`" />
              <span class="ml-2">{{ item.title }}</span>
            </li>
          </ul>

          <!-- 无搜索结果 -->
          <div v-else-if="searchKeyword" class="no-results">
            <p class="no-results__text">未找到匹配的菜单项</p>
          </div>
        </template>

        <!-- 无搜索历史显示 -->
        <div v-if="searchKeyword === '' && searchHistory.length === 0" class="no-history">
          <p class="no-history__text">没有搜索历史</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="ctrl-k-hint">
            <span class="ctrl-k-text">Ctrl+K 快速打开</span>
          </div>
          <div class="shortcuts-group">
            <div class="key-box">
              <div class="key-btn">
                <i class="i-base-enter" />
                <span>选择</span>
              </div>
            </div>
            <div class="arrow-box">
              <div class="arrow-up-down">
                <div class="key-btn">
                  <el-icon><Top /></el-icon>
                </div>
                <div class="key-btn ml-1">
                  <el-icon><Bottom /></el-icon>
                </div>
              </div>
              <span class="key-text">切换</span>
            </div>
            <div class="key-box">
              <div class="key-btn esc-btn">ESC</div>
              <span class="key-text">关闭</span>
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.search-result {
  max-height: 400px;
  overflow-y: auto;

  ul {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 10px;
    text-align: left;
    cursor: pointer;

    &--active {
      color: var(--el-color-primary);
      background-color: var(--el-menu-hover-bg-color);
    }
  }
}

/* 无搜索结果样式 */
.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;

  &__text {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

/* 搜索历史样式 */
.search-history {
  &__title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    font-size: 12px;
    line-height: 34px;
    color: var(--el-text-color-secondary);
  }

  &__clear {
    padding: 2px;
    font-size: 12px;

    &:hover {
      color: var(--el-color-danger);
    }
  }

  &__list {
    padding: 0;
    margin: 0;
  }

  &__icon {
    display: flex;
    align-items: center;
    margin-right: 10px;
    font-size: 16px;
    color: var(--el-text-color-secondary);
  }

  &__name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    color: var(--el-text-color-primary);
    white-space: nowrap;
  }

  &__action {
    padding: 4px;
    color: var(--el-text-color-secondary);
    border-radius: 4px;
    opacity: 0;
    transition: opacity 0.2s;

    &:hover {
      color: var(--el-color-danger);
      background-color: var(--el-fill-color);
    }
  }

  &__item {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 12px;
    cursor: pointer;

    &:hover {
      background-color: var(--el-fill-color-light);

      .search-history__action {
        opacity: 1;
      }
    }
  }
}

/* 没有搜索历史时的样式 */
.no-history {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;

  &__text {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.shortcuts-group {
  display: flex;
  gap: 15px;
  align-items: center;
}

.key-box {
  display: flex;
  gap: 5px;
  align-items: center;
}

.arrow-box {
  display: flex;
  gap: 5px;
  align-items: center;
}

.arrow-up-down {
  display: flex;
  gap: 2px;
  align-items: center;
}

.key-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 20px;
  padding: 0 4px;
  font-size: 12px;
  color: var(--el-text-color-regular);
  background-color: var(--el-fill-color-blank);
  border: 1px solid var(--el-border-color);
  border-radius: 3px;
  box-shadow:
    inset 0 -2px 0 0 var(--el-border-color),
    inset 0 0 1px 1px var(--el-color-white),
    0 1px 2px rgba(30, 35, 90, 0.2);

  &::before {
    position: absolute;
    top: 1px;
    right: 1px;
    left: 1px;
    height: 50%;
    pointer-events: none;
    content: '';
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0));
    border-radius: 2px 2px 0 0;
  }
}

.esc-btn {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 11px;
}

.key-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.ctrl-k-hint {
  display: flex;
  align-items: center;
}

.ctrl-k-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

// 适配Element Plus对话框
:deep(.el-dialog__footer) {
  box-sizing: border-box;
  padding-top: 10px;
  text-align: right;
}

// 暗黑模式适配
html.dark {
  .key-btn::before {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  }
}
</style>
