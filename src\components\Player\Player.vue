<script setup lang="ts">
/**
 * 官方文档
 * @see https://jessibuca.com/api.html
 *
 */

const { videoUrl, loading } = defineProps<{
  videoUrl?: string
  loading?: boolean
}>()
const emit = defineEmits<{ (e: 'timeUpdate', delta: number) }>()

let player: Jessibuca = null

let preTimestamp = 0
function timeUpdate(e: number) {
  const delta = e - preTimestamp
  if (delta < 500) {
    emit('timeUpdate', e - preTimestamp)
  }

  preTimestamp = e
}

watch(
  () => videoUrl,
  (url) => {
    if (url) {
      player.play(videoUrl)
    }
  },
)

const videoContainerRef = useTemplateRef('videoContainerRef')

function init() {
  const player = new Jessibuca({
    container: videoContainerRef.value,
    videoBuffer: 0.1, // 缓存时长
    isResize: false,
    supportDblclickFullscreen: true,
    keepScreenOn: true, // 开启屏幕常亮，在手机浏览器上, canvas标签渲染视频并不会像video标签那样保持屏幕常亮
    hotKey: true, // 否开启键盘快捷键
    controlAutoHide: true, // 底部控制台是否自动隐藏
    useWebFullScreen: true, // 是否使用web全屏(旋转90度)（只会在移动端生效）
    loadingText: '加载中...',
    decoder: '/jessibuca/decoder.js',
    useMSE: true,
    debug: false,
    showBandwidth: false, // 显示网速
    operateBtns: {
      fullscreen: true,
      screenshot: true,
      play: true,
      audio: true,
    },
    isNotMute: false,
    useWCS: false,
    autoWasm: true,
    background: '',
    forceNoOffscreen: false,
    heartTimeout: 5,
    heartTimeoutReplay: true,
    heartTimeoutReplayTimes: 3,
    hiddenAutoPause: false,
    isFlv: false,
    isFullResize: false,
    loadingTimeout: 10,
    loadingTimeoutReplay: true,
    loadingTimeoutReplayTimes: 3,
    openWebglAlignment: false,
    recordType: 'mp4',
    timeout: 10,
    wasmDecodeErrorReplay: true,
    wcsUseVideoRender: true,
  })

  player.on('timeUpdate', timeUpdate)
  if (videoUrl) {
    player.play(videoUrl)
  }

  return player
}

onMounted(() => {
  player = init()
})

onUnmounted(() => {
  if (player) {
    player.destroy().finally(() => {
      player = null
    })
  }
})
</script>

<template>
  <div class="liveVideo rounded-base">
    <div ref="videoContainerRef" v-loading="loading" class=" bg-black rounded-base" />
  </div>
</template>

<style lang="scss" scoped></style>
