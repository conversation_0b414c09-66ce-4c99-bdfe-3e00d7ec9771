<script setup lang="ts">
import type { TreeOption } from '@/api/common.ts'
import { CommonAPI } from '@/api/common.ts'

const emit = defineEmits<{
  (e: 'change', province: number, city: number): void
}>()

const provinceCityList = ref<TreeOption[]>([])
CommonAPI.provinces().then((res) => {
  provinceCityList.value = res
})

const provinceCity = defineModel<{
  province: number
  city: number
}>()

const cityList = computed(() => {
  const selectedProvince = provinceCityList.value.find(item => item.value === provinceCity.value.province)
  return selectedProvince?.children || []
})

function provinceChange() {
  provinceCity.value.city = null
  const { province, city } = provinceCity.value
  emit('change', province, city)
}

function cityChange() {
  const { province, city } = provinceCity.value
  emit('change', province, city)
}
</script>

<template>
  <div class="flex justify-between gap-10">
    <el-select v-model="provinceCity.province" placeholder="选择省份" clearable filterable @change="provinceChange">
      <el-option v-for="item in provinceCityList" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-select v-model="provinceCity.city" placeholder="选择城市" clearable filterable @change="cityChange">
      <el-option v-for="item in cityList" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<style scoped lang="scss">

</style>
