# UploadFile 文件上传组件

## 组件介绍

UploadFile 是一个基于 Element Plus 封装的文件上传组件。

## 基础用法

```vue
<script setup>
const fileList = ref([])
</script>

<template>
  <UploadFile v-model="fileList" />
</template>
```

## Props 属性

| 属性名  | 类型   | 默认值 | 说明                                       |
| ------- | ------ | ------ | ------------------------------------------ |
| dirName | string | 'file' | 上传文件的目录名，用于服务器端文件分类存储 |
| accept  | string | ''     | 接受上传的文件类型，如 '.jpg,.png,.pdf'    |
| tip     | string | -      | 上传提示信息，显示在上传按钮下方           |
| limit   | number | 6      | 最大上传文件数量限制                       |

## v-model

- 组件支持 `v-model` 双向绑定，绑定值类型为 `UploadUserFile[]`：

```typescript
interface UploadUserFile {
  name: string // 文件名
  url: string // 文件的 objectName（服务器存储路径）
  size?: number // 文件大小
}
```

- 含义：图片在服务器上的存储标识（objectName），非完整 URL。
- 组件内部会根据该标识通过 `getFileUrl(objectName)` 拉取实际可访问的图片 URL 用于展示。

## Events 事件

| 事件名 | 说明               | 回调参数                     |
| ------ | ------------------ | ---------------------------- |
| change | 文件列表变化时触发 | (fileList: UploadUserFile[]) |

## 暴露方法

| 方法名   | 说明                 | 返回值  |
| -------- | -------------------- | ------- |
| uploaded | 判断文件是否上传完成 | boolean |

## 功能特性

### 1. 文件预览

- 图片文件：点击后使用内置图片查看器预览
- 其他文件：点击后在新窗口打开

### 2. 文件回显

组件支持编辑场景下的文件回显，自动从服务器获取文件的访问地址。

### 3. 支持查看是否上传完成

通过 `uploaded` 方法可以判断所有文件是否上传完成，方便在表单提交前进行校验。

## 完整示例

### 基础上传

```vue
<script setup>
const fileList = ref([])

function handleFileChange(files) {
  console.log('文件列表变化：', files.value)
}
</script>

<template>
  <UploadFile
    v-model="fileList"
    dirName="documents"
    accept=".pdf,.doc,.docx"
    tip="支持 PDF、Word 文档，单个文件不超过 10MB"
    :limit="3"
    @change="handleFileChange"
  />
</template>
```

### 表单中使用

```vue
<script setup>
const formData = ref({
  attachments: [],
})

function submitForm() {
  // 提交表单数据
  console.log('表单数据：', formData.value)
}
</script>

<template>
  <el-form :model="formData" label-width="5vw">
    <el-form-item label="附件上传">
      <UploadFile v-model="formData.attachments" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>
```

### 文件回显

```vue
<script setup>
const fileList = ref([{ name: 'file.pdf', url: 'path/to/file.pdf' }])
</script>

<template>
  <UploadFile v-model="fileList" />
</template>
```
