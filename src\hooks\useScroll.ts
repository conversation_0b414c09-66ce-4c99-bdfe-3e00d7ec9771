import type { Ref } from 'vue'
import { useEventListener } from '@vueuse/core'

/**
 * 横向滚动组件，支持点击按钮滚动、拖拽滚动、滚轮滚动
 */
export function useScroll(scrollbarRef: Ref<HTMLElement>) {
  const hasScrollBar = ref(false)
  /* 滚动到边界 */
  const arrivedState = reactive({
    left: false,
    right: false,
  })
  onMounted(() => {
    setGrab()
  })
  let stopMouseDown
  let stopMouseUp
  let stopScroll
  let stopMouseMove
  let stopMouseWheel
  function setGrab() {
    hasScrollBar.value = scrollbarRef.value?.scrollWidth > scrollbarRef.value?.clientWidth
    getArrivedState()
    /* 设置鼠标为抓取 */
    if (hasScrollBar.value) {
      scrollbarRef.value.style.cursor = 'grab'
      stopMouseDown = useEventListener(scrollbarRef.value, 'mousedown', (ev: MouseEvent) => {
        scrollbarRef.value.classList.add('grabbing')
        let preX = ev.x
        stopMouseMove = useEventListener('mousemove', (e) => {
          scrollbarRef.value.scrollLeft += preX - e.x
          preX = e.x
        })
      })
      stopMouseUp = useEventListener('mouseup', (e) => {
        e.preventDefault()
        scrollbarRef.value.classList.remove('grabbing')
        stopMouseMove && stopMouseMove()
        stopMouseMove = null
      })
      stopScroll = useEventListener(scrollbarRef.value, 'scroll', getArrivedState)
      /* 滚动滚轮 */
      stopMouseWheel = useEventListener(scrollbarRef.value, 'wheel', (e: WheelEvent) => {
        e.preventDefault()
        total = 0
        if ((e.deltaY > 0 && arrivedState.right) || (e.deltaY < 0 && arrivedState.left))
          return
        runScroll(e.deltaY * 2)
      })
    }
  }

  let raf
  let total = 0
  /* 滚轮移动时平滑滚动 */
  function runScroll(distance) {
    const target = scrollbarRef.value
    const delta = distance / 20
    target.scrollLeft += delta
    total += delta
    if (Math.abs(total) <= Math.abs(distance)) {
      raf = requestAnimationFrame(() => {
        runScroll(distance)
      })
    }
    else {
      cancelAnimationFrame(raf)
    }
  }

  /* 判断是否滚动到边界 */
  function getArrivedState() {
    const target = scrollbarRef.value
    if (target) {
      arrivedState.left = target.scrollLeft <= 0
      arrivedState.right = target.scrollLeft + target.clientWidth >= target.scrollWidth - 1
    }
  }

  function scrollToLeft() {
    const offset = scrollbarRef.value.scrollLeft - scrollbarRef.value.offsetWidth
    scrollbarRef.value.scrollTo({
      left: offset,
      behavior: 'smooth',
    })
  }

  function scrollToRight() {
    const offset = scrollbarRef.value.scrollLeft + scrollbarRef.value.offsetWidth
    scrollbarRef.value.scrollTo({
      left: offset,
      behavior: 'smooth',
    })
  }

  function cleanup() {
    hasScrollBar.value = false
    stopMouseDown && stopMouseDown()
    stopMouseUp && stopMouseUp()
    stopScroll && stopScroll()
    stopMouseMove && stopMouseMove()
    stopMouseWheel && stopMouseWheel()
    arrivedState.left = false
    arrivedState.right = false
    total = 0
    cancelAnimationFrame(raf)
    stopMouseDown = stopMouseUp = stopScroll = stopMouseMove = stopMouseWheel = null
  }

  function refresh() {
    cleanup()
    setGrab()
  }

  return {
    hasScrollBar,
    arrivedState,
    scrollToLeft,
    scrollToRight,
    refresh,
  }
}
