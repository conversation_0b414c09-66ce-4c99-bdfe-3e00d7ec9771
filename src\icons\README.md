# 从 vite-plugin-svg-icons 迁移到 UnoCSS 图标方案

## 🤔 为什么要迁移？

### ⚠️ vite-plugin-svg-icons 的局限性

#### 🐌 1. 开发性能较差

- 实测在引入100个左右svg图标后,打包时间增加了20s左右

#### 🚨 2. 项目维护状态堪忧

- 最近一次更新：2022 年 1 月 29 日（距今已超过 3 年）

#### 🛠️ 3. 只能使用本地图标

- 需要手动下载和维护图标文件

## ✨ UnoCSS 图标方案的优势

### 1. 🌍 生态系统庞大

- **200,000+ 图标**：基于 [Iconify](https://iconify.design/) 作为数据源，提供超过 200,000 个开源图标
- **150+ 图标集**：包含主流的图标库如 Material Design Icons、Ant Design Icons、Tabler Icons 等
- **统一管理**：所有图标通过统一的命名规范管理，无需手动维护

### 2. ⚡ 性能优异

- **纯 CSS 实现**：图标通过纯 CSS 生成，无额外的 JavaScript 运行时开销
- **按需生成**：只生成实际使用的图标，大幅减少包体积
- **高开发性能**：实测100个svg图标几乎未增加打包时间

### 3. 🎨 使用体验极佳

- **类名即图标**：通过简单的 CSS 类名即可使用图标，如 `i-mdi-home`
- **更广的使用范围**：由于其本质就是一个 CSS 类，可以在任何 HTML 元素上使用, 而不是仅限于 Vue 组件

## 📊 技术对比

| 特性     | vite-plugin-svg-icons | UnoCSS 图标方案   |
| -------- | --------------------- | ----------------- |
| 图标数量 | 需手动管理            | 200,000+ 自动可用 |
| 使用方式 | 组件引入              | CSS 类名          |
| 性能开销 | 有 JS 运行时          | 纯 CSS，零开销    |
| 生态系统 | 孤立                  | Iconify 生态      |
| 开发体验 | 优秀                  | 极优秀            |

## 📋 迁移方案

### 🔍 使用方式

直接使用class 类名，类名规则：

```
i-{dirname}-{icon-name}
```

注意：如果图标在根目录下，则是 `i-icon-{icon-name}`

目录结构

```bash
--icons
    --home.svg
    --sun.svg
    --sidebar
        --file.svg
```

```vue
<template>
  <!-- 之前：使用组件方式 -->
  <SvgIcon name="home" />
  <SvgIcon name="sun" />
  <SvgIcon name="sidebar-file" />

  <!-- 现在：使用类名方式 -->
  <div class="i-icon-home" />
  <div class="i-icon-sun" />
  <div class="i-sidebar-file" />
  <!-- 使用iconify里的图标 -->
  <div class="i-svg-spinners:270-ring-with-bg" />
  <div class="i-ep:avatar" />
</template>
```

### 🎯 图标浏览和选择

- 在线浏览：[Icônes](https://icones.js.org/)
- 官方图标集：[Iconify](https://icon-sets.iconify.design/)
- 可以直接在网站上搜索和预览图标

## 📚 参考资料

1. [UnoCSS图标方案最佳实践 - 掘金](https://juejin.cn/post/7494919344754933771?searchId=202507182247576AC27AA762176162C458)
2. [UnoCSS 官方文档](https://unocss.dev/)
