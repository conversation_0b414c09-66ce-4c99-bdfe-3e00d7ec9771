<script setup lang="ts">
import Header from './components/Header.vue'
import Sidebar from './components/Sidebar.vue'
import TagList from './components/TagList.vue'

const scrollbarRef = useTemplateRef('scrollbarRef')
const route = useRoute()

/**
 * 路由切换时，滚动条回到顶部
 */
watch(route, () => {
  scrollbarRef.value.setScrollTop(0)
})
</script>

<template>
  <div class="app size-screen flex">
    <Sidebar class="shrink-0" />
    <main class="app-main h-screen grow flex flex-col">
      <Header class="shrink-0" />
      <TagList />
      <section class="app-container-out grow overflow-hidden relative">
        <el-scrollbar ref="scrollbarRef">
          <router-view v-slot="{ Component }" class="app-container">
            <!--            内置过度动画可选类型 fade slide-left, .slide-right, .slide-top, .slide-bottom，参考transition.scss -->
            <transition name="slide-right" mode="out-in" appear>
              <keep-alive :include="[]">
                <component :is="Component" class="" />
              </keep-alive>
            </transition>
          </router-view>
        </el-scrollbar>
      </section>
    </main>
  </div>
</template>

<style scoped lang="scss">
.app-main {
  //height: calc(100vh - var(--app-header-h));
  width: calc(100vw - var(--app-sidebar-w));

  .app-container-out {
    height: calc(100vh - var(--app-header-h) - var(--gap) - var(--app-tag-list-h));
    padding: var(--gap);
  }
}

.app-container {
  min-height: calc(100vh - var(--app-header-h) - var(--gap) * 3 - var(--app-tag-list-h));
}
</style>
