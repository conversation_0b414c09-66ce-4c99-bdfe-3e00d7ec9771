<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { SYSTEM_TITLE } from '@/constants'
import { useAppStore } from '@/store/app'
import { usePermissionStore } from '@/store/permisson'
import MenuItem from './MenuItem.vue'

const appStore = useAppStore()
const { isCollapse } = storeToRefs(appStore)

const permissionStore = usePermissionStore()
const routes = computed(() => permissionStore.routes.filter(item => !item.meta.hidden))

const route = useRoute()

/**
 *  计算当前激活的菜单路径
 */
const activeMenuPath = computed(() => {
  // 如果路由meta中指定了activeMenu，则使用指定的
  if (route.meta.activeMenu) {
    return route.meta.activeMenu as string
  }

  // 否则使用当前路径
  return route.path
})

const router = useRouter()
function toHome() {
  router.push({ path: '/' })
}
</script>

<template>
  <div class="app-sidebar flex flex-col bg-$el-bg-color" :class="isCollapse ? 'w-$app-sidebar-w-small' : 'w-$app-sidebar-w' ">
    <aside class="app-title overflow-hidden flex items-center px-16 flex justify-center cursor-pointer" @click="toHome">
      <img src="@/assets/logo.webp" class="size-20" alt="" />
      <span v-if="!isCollapse" class="ml-15 whitespace-nowrap font-bold tracking-1.5" text="textPrimary">{{ SYSTEM_TITLE }}</span>
    </aside>

    <el-scrollbar>
      <el-menu
        class="pb-20"
        :collapse="isCollapse"
        :collapse-transition="false"
        :default-active="activeMenuPath"
      >
        <template v-for="menuRoute in routes" :key="menuRoute.path">
          <MenuItem :routeData="menuRoute" :rootPath="menuRoute.path" :level="0" />
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<style scoped lang="scss">
.app-sidebar {
  // width: var(--app-sidebar-w);
  transition: var(--el-transition-duration) width ease-in-out;

  border-right: 1px solid var(--el-border-color-light);
  background: var(--Fill-Blank, #fff);
  box-shadow: var(--Shadow-Num-0, 0) var(--Shadow-Num-0, 0) var(--Shadow-Num-12, 12px) var(--Shadow-Num-0, 0)
    var(--Shadow-12, rgba(0, 0, 0, 0.12));

  .el-menu {
    // min-width: var(--app-sidebar-w);
  }
}

.app-title {
  height: var(--app-header-h);
}

:deep(.el-menu) {
  border-right: none;
  .el-menu-item.is-active {
    border-right: 4px solid var(--el-color-primary) !important;
    background: var(--el-color-primary-9);
  }
}
</style>
