<script setup lang="ts">
import { CircleClose } from '@element-plus/icons-vue'
import HorizontalScroll from '@/components/HorizontalScroll/HorizontalScroll.vue'

const route = useRoute()
interface RouteInfo {
  path: string
  title: string
  icon: string
}

const routeList = ref<RouteInfo[]>([])

const router = useRouter()
function closeTag(path: string) {
  // 关闭的是否是当前路由
  const isCurrent = route.path === path
  if (isCurrent) {
    const index = routeList.value.findIndex(item => item.path === path)
    routeList.value = routeList.value.filter(item => item.path !== path)
    const prevRoute = routeList.value[Math.max(index - 1, 0)]
    router.push(prevRoute.path)
  }
  else {
    routeList.value = routeList.value.filter(item => item.path !== path)
  }
}

function to(path: string) {
  if (route.path !== path) {
    router.push(path)
  }
}

watch(route, () => {
  if (routeList.value.some(item => item.path === route.path))
    return

  const { title, icon } = route.meta
  routeList.value.push({ path: route.path, title, icon })
}, { immediate: true })
</script>

<template>
  <div class="tagList relative whitespace-nowrap">
    <HorizontalScroll :show-arrow="false">
      <transition-group name="tabbar">
        <button v-for="item in routeList" :key="item.path" class="tag" :class="{ active: route.path === item.path }" @click="to(item.path)">
          <i v-if="item.icon" :class=" `i-sidebar-${item.icon}` " />
          <span>{{ item.title }}</span>
          <el-icon v-if="Object.keys(routeList).length > 1" class="cursor-pointer closeIcon" @click.stop="closeTag(item.path)"><CircleClose /></el-icon>
        </button>
      </transition-group>
    </HorizontalScroll>
  </div>
</template>

<style scoped lang="scss">
.tagList {
  padding: var(--gap) var(--gap) 0;
  .tag {
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    gap: 8px;
    vertical-align: middle;
    height: var(--app-tag-list-h);
    padding: var(--Space-4, 4px) var(--Space-Large-16, 16px);

    border-radius: var(--el-border-radius-base);
    background: var(--Fill-Blank, #fff);

    color: var(---el-text-color-regular);
    cursor: pointer;

    .closeIcon {
      &:hover {
        transition: all 0.1s;
        transform: scale(1.1);
      }
    }

    &.active {
      color: var(--el-color-primary);
    }
  }
}
</style>
