import type { NavigationGuardNext, RouteLocationNormalized, RouteRecordRaw } from 'vue-router'
import NProgress from 'nprogress'
import { FileAPI } from '@/api/file.ts'
import { SYSTEM_TITLE } from '@/constants'
import router, { getFirstRoutePath } from '@/router'
import { usePermissionStore } from '@/store/permisson'
import { useUserStore } from '@/store/user'
import { TOKEN_KEY } from '@/utils/request'
import 'nprogress/nprogress.css'

// 白名单路由
const whiteList = ['/login']
NProgress.configure({
// 动画方式
  easing: 'ease',
  // 递增进度条的速度
  speed: 500,
  // 是否显示加载ico
  showSpinner: false,
  // 自动递增间隔
  trickleSpeed: 200,
  // 初始化时的最小百分比
  minimum: 0.3,
})

router.beforeEach(async (to, from, next) => {
  /**
   * 利用最新的过渡API添加路由切换时的过渡效果 startViewTransition
   *@see https://developer.mozilla.org/en-US/docs/Web/API/Document/startViewTransition
   */
  // Fallback for browsers that don't support View Transitions:
  if (document.startViewTransition) {
    // With View Transitions:
    document.startViewTransition(() => {
      return true
    })
  }

  NProgress.start()
  const hasToken = localStorage.getItem(TOKEN_KEY)

  if (hasToken) {
    if (to.path === '/login') {
      // 如果已登录，跳转到首页
      next({ path: '/' })
    }
    else {
      const userStore = useUserStore()
      const hasRoles = userStore.user.roles && userStore.user.roles.length > 0

      const permissionStore = usePermissionStore()
      if (hasRoles) {
        if (to.path === '/') {
          next({ path: getFirstRoutePath(permissionStore.routes) })
        }
        else {
          next()
        }
      }
      else {
        try {
          const userInfo = await userStore.getUserInfo()
          await FileAPI.init()

          const dynamicRoutes = permissionStore.generateRoutes(userInfo.menus)
          for (const route of dynamicRoutes) {
            permissionStore.removeRoutesFn.push(router.addRoute(route))
          }

          next({ ...to, replace: true })
        }
        catch {
          // 移除 token 并重定向到登录页，携带当前页面路由作为跳转参数
          await userStore.resetToken()
          redirectToLogin(to, next)
        }
      }
    }
  }

  else {
    // 未登录
    if (whiteList.includes(to.path)) {
      next() // 在白名单，直接进入
    }
    else {
      // 不在白名单，重定向到登录页
      redirectToLogin(to, next)
    }
  }
})

router.afterEach((to) => {
  const metaTitle = to.meta.title
  document.title = `${SYSTEM_TITLE}${metaTitle ? `-${to.meta.title}` : ''}`
  NProgress.done()
})

/** 重定向到登录页 */
function redirectToLogin(to: RouteLocationNormalized, next: NavigationGuardNext) {
  const params = new URLSearchParams(to.query as Record<string, string>)
  const queryString = params.toString()
  const redirect = queryString ? `${to.path}?${queryString}` : to.path
  next(`/login?redirect=${encodeURIComponent(redirect)}`)
}
