body {
  --el-menu-bg-color: transparent;

  .el-table {
    --el-table-header-bg-color: var(--el-fill-color-light);
    --el-table-row-hover-bg-color: var(--el-fill-color-lighter);
    --el-table-text-color: var(--el-text-color-primary);

    .el-table__cell {
      padding: 9px 0;
    }
    .el-table__cell {
      border-right: var(--el-table-border) !important;
    }
  }

  .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: var(--el-table-header-bg-color);
  }

  .el-input,
  .el-textarea {
    --el-input-bg-color: var(--el-bg-color);
  }

  .el-textarea .el-input__count {
    background: var(--el-bg-color);
  }

  .el-select__wrapper {
    background-color: var(--el-bg-color);
  }

  .el-card {
    --el-card-bg-color: var(--el-bg-color);
  }

  .el-form-item {
    margin-bottom: 0;
  }

  .el-dialog {
    .el-form-item {
      margin-bottom: 22px;
    }
  }
}

// 表格无数据展示
.el-table {
  .cell {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
}
