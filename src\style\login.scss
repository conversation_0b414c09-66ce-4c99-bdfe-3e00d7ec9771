.login-container {
  display: flex;
  // align-items: center;
  justify-content: flex-end;
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  background: url(@/assets/home/<USER>
  background-size: cover;

  .top-bar {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    padding: 10px;
  }

  .login-card {
    box-sizing: border-box;
    width: 562px;
    height: 100vh;
    padding: 55px 76px 55px 116px;
    background: rgba($color: #fff, $alpha: 0.5);
    border: none;
    // border-radius: 4%;
    border-radius: 0;

    @media (width <= 640px) {
      width: 340px;
    }

    .input-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .captcha-image {
      height: 48px;
      cursor: pointer;
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }
  }

  .el-form-item {
    margin-bottom: 40px;
    background: var(--el-input-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 5px;
  }

  .el-form-item__error {
    padding-top: 10px;
    // padding-left: 56px;
    // font-size: 14px;
  }

  .el-input {
    .el-input__wrapper {
      padding: 0;
      background-color: transparent;
      box-shadow: none;

      &.is-focus,
      &:hover {
        box-shadow: none !important;
      }

      input:-webkit-autofill {
        /* 通过延时渲染背景色变相去除背景颜色 */
        transition: background-color 1000s ease-in-out 0s;
      }
    }

    .el-input__inner {
      // color: #1339d5;
      font-size: 17px;
      color: #201c1c;
    }
  }
}
