import type { AddrSearchInfo, LocateInfo, TianDiTuAddrInfo } from '#/map'
import { TIANDITU_KEY } from '@/constants'

/**
 * 判单是否是图片
 */
export function isImg(url: string) {
  return /\.(jpg|jpeg|png|gif|bmp|webp|svg|jfif|ico)$/i.test(url)
}

/**
 * 下载文件，支持传入文件流或者url
 */
export async function download(content: string | BlobPart, fileName: string) {
  try {
    let blob: Blob
    if (typeof content === 'string') {
      // 获取文件
      const response = await fetch(content)
      blob = await response.blob()
    }
    else {
      blob = new Blob([content])
    }

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'

    // 设置下载属性
    link.href = downloadUrl
    link.download = fileName

    // 触发下载
    document.body.append(link)
    link.click()

    // 清理
    link.remove()
    URL.revokeObjectURL(downloadUrl)
  }
  catch (error) {
    console.error('下载文件失败:', error)
  }
}

/**
 * 经纬度转地址
 */
const tianToken = 'df88e602c4cba7497f8eb525f458a98c'
export async function latLngToAddress(latitude: number, longitude: number) {
  // const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`
  const url = `https://api.tianditu.gov.cn/geocoder?postStr={'lon':${longitude},'lat':${latitude},'ver':1}&type=geocode&tk=${tianToken}`

  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data: LocateInfo = await response.json()
    return data.result.formatted_address // 返回位置信息
  }
  catch (error) {
    console.error('Error:', error)
    return null
  }
}

/**
 *
 * 天地图
 * 地址 =》 经纬度
 */

export async function searchAddress(address: string, province?: number, city?: number) {
  const url = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${address}","queryType":12,"start":0,"count":10,"show":2,"specify":"${city || province || 156000000}"}&type=query&tk=${TIANDITU_KEY}`

  try {
    const response = await fetch(url)
    const data: TianDiTuAddrInfo = await response.json()
    const statusCode = data.status.infocode
    if (statusCode === 1000) {
      if (data.area) {
        data.area.address = data.area.name
        data.pois ??= []
        data.pois.push(data.area)
      }

      if (!data.pois) {
        throw new Error('没有搜索到相关地址')
      }

      for (const item of data.pois) {
        const [longitude, latitude] = item.lonlat.split(',').map(Number)
        item.lat = latitude
        item.lng = longitude
        item.fullAddress = `${item.province || ''}${item.city || ''}${item.address}`
      }

      return data.pois
    }
    else {
      throw new Error('没有搜索到相关地址')
    }
  }
  catch (error) {
    ElMessage.error('没有搜索到相关地址')
    throw new Error(error)
  }
}

/**
 * 高德地图地址搜索
 */
// const amapServiceKeyList = ['c8ee7a9e90f6c679f3348e284a86a5c8', '5755195ee8340f609b735cd11a59dfcb', '18299e5c1960a93f0d991686d6b53478']
// const STORAGE_KEY = 'map-key-index'
// let keyIndex = Number(localStorage.getItem(STORAGE_KEY)) || 0
// // 记录尝试次数
// let tryCount = 0
// export async function searchAddress(address: string, province?: number, city?: number) {
//     // https://restapi.amap.com/v3/geocode/geo
//     const url = `https://restapi.amap.com/v5/place/text?key=${amapServiceKeyList[keyIndex]}&keywords=${address}`
//
//
//     try {
//         const response = await fetch(url)
//         if (!response.ok) {
//             throw new Error('Network response was not ok')
//         }
//
//         const data: AddrSearchInfo = await response.json()
//         if (data.status === '1') {
//             tryCount = 0
//             return data.pois // 返回poi列表
//         }
//         else if (tryCount < amapServiceKeyList.length) {
//             tryCount++
//             // 失效后切换key
//             keyIndex = (keyIndex + 1) % amapServiceKeyList.length
//             localStorage.setItem(STORAGE_KEY, `${keyIndex}`)
//             return searchAddress(address)
//         }
//         else {
//             ElMessage.error('地理编码服务失效，请联系管理员')
//             throw new Error('地理编码服务失效，请联系管理员')
//         }
//     }
//     catch (error) {
//         throw new Error(error)
//     }
// }

/**
 * 传入毫秒数，输出天时分秒
 * @param milliseconds 毫秒数
 * @returns 天时分秒
 * 示例用法：
 * formatDuration(30000)     // "30秒"
 * formatDuration(90000)     // "1分30秒"
 * formatDuration(3661000)   // "1小时1分1秒"
 * formatDuration(90061000)  // "1天1小时1分1秒"
 */
export function formatDuration(milliseconds: number) {
  if (!milliseconds)
    return '--'

  // 将毫秒转换为秒
  const totalSeconds = Math.floor(milliseconds / 1000)

  // 不足一分钟，直接返回秒
  if (totalSeconds < 60) {
    return `${totalSeconds}秒`
  }

  // 计算各个时间单位
  const days = Math.floor(totalSeconds / 86400) // 86400 = 24 * 60 * 60
  const hours = Math.floor((totalSeconds % 86400) / 3600) // 3600 = 60 * 60
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  let result = ''

  if (days > 0) {
    result += `${days}天`
  }

  if (hours > 0) {
    result += `${hours}小时`
  }

  if (minutes > 0) {
    result += `${minutes}分`
  }

  if (seconds > 0) {
    result += `${seconds}秒`
  }

  return result || '0秒'
}
