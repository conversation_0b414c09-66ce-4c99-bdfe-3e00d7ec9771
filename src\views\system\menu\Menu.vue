<script setup lang="ts">
import type { MenuVO } from '@/api/system/menu'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { MenuAPI, MenuTypeEnum } from '@/api/system/menu'
import { getState } from '@/hooks'

// 移除多语言功能
const route = useRoute()
const state = getState<MenuVO>()
const { queryParams, tableList, loading, dialogVisible, formData, title, submitting } = toRefs(state)
// 表单验证规则
const rules = {
  parentId: [{ required: true, message: '请选择顶级菜单', trigger: 'blur' }],
  name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
  routeName: [{ required: true, message: '请输入路由名称', trigger: 'blur' }],
  routePath: [{ required: true, message: '请输入路由路径', trigger: 'blur' }],
  component: [{ required: true, message: '请输入组件路径', trigger: 'blur' }],
  visible: [{ required: true, message: '请输入路由路径', trigger: 'blur' }],
}

function handleQuery() {
  loading.value = true
  MenuAPI.list(queryParams.value).then((res) => {
    tableList.value = res
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()
const colorMap = {
  2: '#f1b000',
  1: '#019e59',
  4: '#ff1d1d',
}

const searchFormRef = useTemplateRef('searchFormRef')
function refreshSearch() {
  searchFormRef.value.resetFields()
  handleQuery()
}

/**
 * 复制功能
 */
function handleCopy(str: string) {
  navigator.clipboard.writeText(str).then(() => {
    ElMessage.success({ message: `${str}复制成功`, grouping: true })
  })
}

// 顶级菜单下拉选项
const menuOptions = computed(() => {
  return [{ id: '0', name: '顶级菜单', children: tableList.value }]
})

function add(row?: typeof state.formData) {
  let parentId = null
  let sort: number
  if (row) {
    sort = row.children.length + 1
    parentId = row.id
  }
  else {
    sort = tableList.value.length + 1
  }

  formData.value = {
    type: MenuTypeEnum.MENU, // 默认菜单
    sort,
    parentId,
  }

  title.value = '新增'
  dialogVisible.value = true
}

function edit(row: typeof state.formData) {
  formData.value = { ...row }
  title.value = '编辑'
  dialogVisible.value = true
}

const formRef = useTemplateRef('formRef')
function submit() {
  formRef.value.validate().then(() => {
    submitting.value = true
    const menuId = formData.value.id
    formData.value.type = Number(formData.value.type)
    const fn = menuId ? MenuAPI.update : MenuAPI.add
    fn({ ...formData.value, visible: 1 }).then(() => {
      ElMessage.success('操作成功！')
      dialogVisible.value = false
      handleQuery()
    }).finally(() => {
      submitting.value = false
    })
  })
}

function handleDelete(menuId: number) {
  ElMessageBox.confirm('确认删除已选中的数据项?', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    loading.value = true
    MenuAPI.deleteById(menuId)
      .then(() => {
        ElMessage.success('操作成功！')
        handleQuery()
      })
      .finally(() => {
        loading.value = false
      })
  },
  )
}
</script>

<template>
  <div class="table-page">
    <header class="page-header">
      <el-form ref="searchFormRef" inline :model="queryParams" @submit.prevent>
        <el-form-item label="关键字" prop="keywords">
          <el-input v-model="queryParams.keywords" placeholder="菜单名称" @change="handleQuery" />
        </el-form-item>
      </el-form>
      <el-button :icon="Refresh" @click="refreshSearch">重置</el-button>
    </header>

    <main v-loading="loading" class="page-main">
      <header class="flex mb-24">
        <span class="font-bold" text="18 textPrimary">{{ route.meta.title }}</span>
        <el-button class="ml-auto" type="primary" :icon="Plus" @click="add(null)">新增</el-button>
      </header>
      <el-table
        :data="tableList"
        highlight-current-row
        border
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        row-class-name="!h-60"
      >
        <el-table-column prop="name" label="菜单名称" min-width="200" />

        <el-table-column label="类型" align="center" min-width="80">
          <template #default="scope">
            <!-- {{ scope.row.type }}{{ MenuTypeEnum.CATALOG }} -->
            <el-tag v-if="scope.row.type === MenuTypeEnum.CATALOG" type="warning">目录</el-tag>
            <el-tag v-if="scope.row.type === MenuTypeEnum.MENU" type="success">菜单</el-tag>
            <el-tag v-if="scope.row.type === MenuTypeEnum.BUTTON" type="danger">按钮</el-tag>
            <el-tag v-if="scope.row.type === MenuTypeEnum.EXTLINK" type="info">外链</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="权限标识" align="center" min-width="200" prop="perm">
          <template #default="{ row }">
            <span :style="{ color: colorMap[row.type] }">{{ row.perm }}</span>
            <el-button v-if="row.perm" size="small" type="primary" class="ml-6" @click="handleCopy(row.perm)">复制</el-button>
          </template>
        </el-table-column>

        <el-table-column label="排序" align="center" min-width="80" prop="sort" />

        <el-table-column fixed="right" align="center" label="操作" min-width="220">
          <template #default="scope">
            <el-link
              v-if="scope.row.type == MenuTypeEnum.CATALOG || scope.row.type == MenuTypeEnum.MENU"
              type="primary"
              underline="never"
              @click.stop="add(scope.row)"
            >
              新增
            </el-link>
            <el-link class="mx-15" type="primary" underline="never" @click="edit(scope.row)">编辑</el-link>
            <el-link type="danger" underline="never" @click="handleDelete(scope.row.id)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </main>

    <el-dialog v-model="dialogVisible" destroy-on-close width="40vw" :title="title" append-to-body :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="4.3vw" class="p-30" size="large">
        <el-form-item label="父级菜单" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            placeholder="选择上级菜单"
            :data="menuOptions"
            filterable
            check-strictly
            value-key="id"
            :render-after-expand="false"
            :props="{ label: 'name' }"
          />
        </el-form-item>

        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入菜单名称" />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="formData.type">
            <!-- 1-菜单；2-目录；3-外链；4-按钮权限 -->
            <!--  CATALOG  MENU  EXTLINK   BUTTON -->
            <el-radio :value="2">目录</el-radio>
            <el-radio :value="1">菜单</el-radio>
            <el-radio :value="4">按钮</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" class="w-100" controls-position="right" :min="0" />
        </el-form-item>

        <!-- 权限标识 -->
        <el-form-item label="权限标识" prop="perm">
          <el-input v-model="formData.perm" placeholder="sys:user:add" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button :loading="submitting" type="primary" @click="submit">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
