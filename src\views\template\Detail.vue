<script setup lang="ts">
// 用户信息数据
const userInfo = ref({
  username: '这里是用户名呀',
  phone: '+86 1234567890',
  location: '成都',
  email: '<EMAIL>',
  profile: 'https://element-plus.org',
  language: '英语',
  tags: ['设计', '学校'],
  address: '四川省成都市金牛区金牛路金牛街399号',
})

// 退款申请数据
const refundInfo = ref({
  pickupOrderNo: '100000000',
  status: '已取货',
  saleOrderNo: '1231313144',
  subOrderNo: '58789999652',
})

// 退款申请表格数据
const refundTableData = ref([
  {
    productCode: 'CS-3487329',
    productName: '矿泉水 550ml',
    barcode: '6864 5537 3823 6553 746',
    price: 2,
    quantity: 13,
    amount: 26,
  },
  {
    productCode: 'CS-3487329',
    productName: '好吃的薯片',
    barcode: '6864 5537 3823 6553 746',
    price: 29,
    quantity: 980,
    amount: 28420,
  },
  {
    productCode: 'CS-3487329',
    productName: '特别好吃的蛋卷',
    barcode: '6864 5537 3823 6553 746',
    price: 34,
    quantity: 124,
    amount: 4216,
  },
  {
    productCode: 'CS-3487329',
    productName: '红烧排骨',
    barcode: '6864 5537 3823 6553 746',
    price: 21,
    quantity: 788,
    amount: 16548,
  },
  {
    productCode: 'CS-3487329',
    productName: '美式烤肉',
    barcode: '6864 5537 3823 6553 746',
    price: 9,
    quantity: 589,
    amount: 5301,
  },
])

// 退款进度表格数据
const refundProgressData = ref([
  {
    time: '2024-01-15 10:30:00',
    operator: '张三',
    status: '已提交',
    detail: '用户提交退款申请',
    remark: '商品有质量问题',
  },
  {
    time: '2024-01-15 14:20:00',
    operator: '李四',
    status: '审核中',
    detail: '客服审核中',
    remark: '正在核实情况',
  },
  {
    time: '2024-01-16 09:15:00',
    operator: '王五',
    status: '审核通过',
    detail: '退款申请已通过',
    remark: '同意退款',
  },
  {
    time: '2024-01-16 15:45:00',
    operator: '系统',
    status: '退款中',
    detail: '财务处理中',
    remark: '预计1-3个工作日到账',
  },
  {
    time: '2024-01-17 10:00:00',
    operator: '系统',
    status: '已退款',
    detail: '退款已到账',
    remark: '退款成功',
  },
])

function getSummaries(param: any) {
  const { columns, data } = param
  const sums: string[] = []
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '总计'
      return
    }

    if (column.property === 'quantity') {
      const values = data.map((item: any) => Number(item[column.property]))
      sums[index] = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (Number.isNaN(value)) {
          return prev
        }
        else {
          return prev + curr
        }
      }, 0).toString()
    }
    else if (column.property === 'amount') {
      const values = data.map((item: any) => Number(item[column.property]))
      const sum = values.reduce((prev: number, curr: number) => {
        const value = Number(curr)
        if (Number.isNaN(value)) {
          return prev
        }
        else {
          return prev + curr
        }
      }, 0)
      sums[index] = `¥${sum.toLocaleString()}`
    }
    else {
      sums[index] = ''
    }
  })
  return sums
}
</script>

<template>
  <div class="flex flex-col gap-$gap">
    <!-- 用户信息卡片 -->
    <section class="bg-white rounded-4">
      <header p="x-24 y-16">
        <h3 text="16 black" font="500" leading="24">用户信息</h3>
      </header>
      <div p="x-24 b-16">
        <!-- 第一行 -->
        <div class="flex">
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">用户名</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ userInfo.username }}</span>
          </div>
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">电话</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ userInfo.phone }}</span>
          </div>
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">位置</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ userInfo.location }}</span>
          </div>
        </div>
        <!-- 第二行 -->
        <div class="flex">
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">电子邮件</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ userInfo.email }}</span>
          </div>
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">个人资料</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ userInfo.profile }}</span>
          </div>
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">语言</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ userInfo.language }}</span>
          </div>
        </div>
        <!-- 第三行 -->
        <div class="flex">
          <div class="flex items-center h-40" style="width: 33.33%">
            <span class="flex items-center h-full py-6" text="14 text-primary">备注</span>
            <div class="ml-16 flex gap-8">
              <el-tag v-for="tag in userInfo.tags" :key="tag" type="primary" size="small">{{ tag }}</el-tag>
            </div>
          </div>
          <div class="flex items-center h-40" style="width: 66.67%">
            <span class="flex items-center h-full py-6" text="14 text-primary">地址</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ userInfo.address }}</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 退款申请卡片 -->
    <section class="bg-white rounded-4">
      <header p="x-24 y-16">
        <h3 text="16 black" font="500" leading="24">退款申请</h3>
      </header>
      <div p="x-24 b-16">
        <!-- 第一行 -->
        <div class="flex">
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">取货单号</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ refundInfo.pickupOrderNo }}</span>
          </div>
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">状态</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ refundInfo.status }}</span>
          </div>
          <div class="flex-1 flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">销售单号</span>
            <span class="ml-16 flex-1" text="14 text-regular">{{ refundInfo.saleOrderNo }}</span>
          </div>
        </div>
        <!-- 第二行 -->
        <div class="flex">
          <div class="flex items-center h-40">
            <span class="flex items-center h-full py-6" text="14 text-primary">子单号</span>
            <span class="ml-16" text="14 text-regular">{{ refundInfo.subOrderNo }}</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 退款申请表格卡片 -->
    <section class="bg-white rounded-4">
      <header p="x-24 y-16">
        <h3 text="16 black" font="500" leading="24">退款申请</h3>
      </header>
      <div class="pb-16 px-24">
        <el-table
          :data="refundTableData"
          stripe
          highlight-current-row
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column prop="productCode" label="商品编号" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <el-link type="primary" underline="never">{{ row.productCode }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="productName" label="商品名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="barcode" label="商品条码" min-width="180" show-overflow-tooltip />
          <el-table-column prop="price" label="单价" min-width="100" show-overflow-tooltip>
            <template #default="{ row }">
              ¥{{ row.price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量（件）" min-width="100" show-overflow-tooltip />
          <el-table-column prop="amount" label="总金额" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              ¥{{ row.amount.toLocaleString() }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </section>

    <!-- 退款进度表格卡片 -->
    <section class="bg-white rounded-4">
      <header p="x-24 y-16">
        <h3 text="16 black" font="500" leading="24">退款进度</h3>
      </header>
      <div class="pb-16 px-24">
        <el-table
          :data="refundProgressData"
          stripe
          highlight-current-row
        >
          <el-table-column prop="time" label="时间" min-width="160" show-overflow-tooltip />
          <el-table-column prop="operator" label="操作人" min-width="100" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" min-width="100" show-overflow-tooltip>
            <template #default="{ row }">
              <el-tag
                :type="row.status === '已退款' ? 'success' : row.status === '退款中' ? 'warning' : 'info'"
                size="small"
              >
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="detail" label="操作详情" min-width="140" show-overflow-tooltip />
          <el-table-column prop="remark" label="备注" min-width="140" show-overflow-tooltip />
        </el-table>
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">
</style>
