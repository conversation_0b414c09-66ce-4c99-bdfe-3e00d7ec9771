<script setup lang="ts">
import type { FormInstance, FormRules, UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'

const formRef = useTemplateRef('formRef')

// 表单数据
const formData = reactive({
  input1: '',
  input2: '',
  input3: '',
  select1: '',
  select2: '',
  select3: '',
  select4: '',
  date: '',
  switch: true,
  checkbox: ['复选框', '选项'],
  radio: '种类一',
  textarea: '',
  fileList: [] as UploadUserFile[],
})

// 表单验证规则
const rules = reactive<FormRules>({
  input1: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  input2: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  input3: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  select1: [{ required: true, message: '请选择', trigger: 'change' }],
  select2: [{ required: true, message: '请选择', trigger: 'change' }],
})

// 提交表单
async function submitForm(formEl: FormInstance | undefined) {
  if (!formEl)
    return
  await formEl.validate((valid) => {
    if (valid) {
      ElMessage.success('提交成功')
    }
    else {
      ElMessage.error('请填写完整的表单信息')
    }
  })
}

// 重置表单
function resetForm(formEl: FormInstance | undefined) {
  if (!formEl)
    return
  formEl.resetFields()
}

// 文件列表
const fileList = ref<UploadUserFile[]>([
  {
    name: 'ai-generated-8834900_1280.jpg',
    url: 'file/2025-09-16/ai-generated-8834900_1280.jpg',
    size: 188313,
  },
  {
    name: 'ai-generated-8843480_1280.jpg',
    url: 'file/2025-09-16/ai-generated-8843480_1280.jpg',
    size: 157610,
  },
  {
    name: 'background-2482325_1280.jpg',
    url: 'file/2025-09-16/background-2482325_1280.jpg',
    size: 538255,
  },
])

function fileChange() {
  console.log(fileList.value)
}

const uploadRef = useTemplateRef('uploadRef')
function imgChange(value) {
  console.log(value)
  console.log(uploadRef.value?.isAllUploaded())
}

const imgList = ref([
  {
    name: 'ai-generated-8834900_1280.jpg',
    url: 'file/2025-09-16/ai-generated-8834900_1280.jpg',
    size: 188313,
  },
  {
    name: 'ai-generated-8843480_1280.jpg',
    url: 'file/2025-09-16/ai-generated-8843480_1280.jpg',
    size: 157610,
  },
  {
    name: 'background-2482325_1280.jpg',
    url: 'file/2025-09-16/background-2482325_1280.jpg',
    size: 538255,
  },

])

const imgUrl = ref('file/2025-09-16/ai-generated-8834900_1280.jpg')
</script>

<template>
  <el-card>
    <template #header>
      <div class="flex items-center leading-extra-large">
        <span class="font-bold" text="20">基础表单</span>
        <div class="w-1 h-12 bg-border mx-12" />
        <span text="text-secondary">这是描述文字</span>
      </div>
    </template>
    <!--    内容部分 -->
    <div class="flex justify-center" p="y-40 x-20">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="6vw"
        label-position="right"
        class="w-600 flex flex-col gap-y-22"
      >
        <!-- 输入框1 - 带必填和信息提示 -->
        <el-form-item label="这里是标题" prop="input1">
          <el-input v-model="formData.input1" placeholder="请输入" />
        </el-form-item>

        <!-- 输入框2 - 必填 -->
        <el-form-item label="这里是标题" prop="input2">
          <el-input v-model="formData.input2" placeholder="请输入" />
        </el-form-item>

        <!-- 输入框3 - 带必填、信息提示和底部提示文字 -->
        <el-form-item label="这里是标题" prop="input3">
          <el-input v-model="formData.input3" placeholder="请输入" />
        </el-form-item>

        <!-- 选择框1 - 聚焦状态 -->
        <el-form-item label="这里是标题" prop="select1">
          <el-select v-model="formData.select1" placeholder="请选择" class="w-full focus-select">
            <el-option label="选项1" value="1" />
            <el-option label="选项2" value="2" />
            <el-option label="选项3" value="3" />
          </el-select>
        </el-form-item>

        <!-- 选择框2 - 错误状态 -->
        <el-form-item label="这里是标题" prop="select2">
          <el-select v-model="formData.select2" placeholder="请选择" class="w-full">
            <el-option label="选项1" value="1" />
            <el-option label="选项2" value="2" />
          </el-select>
        </el-form-item>

        <!-- 选择框3 - 禁用状态 -->
        <el-form-item label="这里是标题">
          <el-select v-model="formData.select3" placeholder="请选择" disabled class="w-full">
            <el-option label="选项1" value="1" />
            <el-option label="选项2" value="2" />
          </el-select>
        </el-form-item>

        <!-- 选择框4 - 已填写状态 -->
        <el-form-item label="这里是标题">
          <el-select v-model="formData.select4" placeholder="请选择" class="w-full">
            <el-option label="这是已经输入的内容" value="content" />
            <el-option label="选项2" value="2" />
          </el-select>
        </el-form-item>

        <!-- 日期选择器 -->
        <el-form-item label="这里是标题">
          <el-date-picker
            v-model="formData.date"
            type="date"
            placeholder="选择日期"
            class="!w-full"
          />
        </el-form-item>

        <!-- 开关 -->
        <el-form-item label="这里是开关">
          <el-switch v-model="formData.switch" />
        </el-form-item>

        <!-- 复选框组 -->
        <el-form-item label="复选框">
          <el-checkbox-group v-model="formData.checkbox">
            <el-checkbox label="复选框" />
            <el-checkbox label="选项" />
            <el-checkbox label="复选框2" />
          </el-checkbox-group>
        </el-form-item>

        <!-- 单选框组 -->
        <el-form-item label="单选框">
          <el-radio-group v-model="formData.radio">
            <el-radio label="种类一" />
            <el-radio label="种类二" />
            <el-radio label="种类三" />
          </el-radio-group>
        </el-form-item>

        <!-- 文本域 -->
        <el-form-item label="描述文本框">
          <el-input
            v-model="formData.textarea"
            type="textarea"
            :rows="3"
            placeholder="请输入..."
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <!-- 文件上传 -->
        <el-form-item label="上传文件">
          <UploadFile v-model="fileList" @change="fileChange" />
        </el-form-item>

        <el-form-item label="上传多张图片">
          <UploadImage v-model="imgList" @change="imgChange" />
        </el-form-item>

        <el-form-item label="上传单张图片">
          <UploadSingleImage ref="uploadRef" v-model="imgUrl" @change="imgChange" />
        </el-form-item>

        <!-- 按钮组 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm(formRef)">保存</el-button>
          <el-button @click="resetForm(formRef)">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.form-container {
  // 聚焦状态样式
  .focus-select :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #0d42ff inset;
  }
}
</style>
