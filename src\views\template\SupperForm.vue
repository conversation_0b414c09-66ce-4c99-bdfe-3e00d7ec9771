<script setup lang="ts">
import type { User } from '@/api/system/user.ts'
import { Plus } from '@element-plus/icons-vue'
import { UserAPI } from '@/api/system/user.ts'
import { getState } from '@/hooks'

const state = getState<User>()
const { queryParams, tableList, loading, dialogVisible, formData, title, total, rules, submitting } = toRefs(state)

// 表单1数据
const form1Data = ref({
  name1: '',
  name2: '',
  date: '',
  dateRange: [],
  website1: '',
  website2: '',
  name3: '',
  name4: '',
})

// 表单2数据
const form2Data = ref({
  name1: '',
  name2: '',
  date: '',
  dateRange: [],
  website1: '',
  website2: '',
  name3: '',
  name4: '',
})
rules.value = {
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  nickname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  roleName: [{ required: true, message: '请选择角色', trigger: 'change' }],
}

function handleQuery() {
  loading.value = true
  UserAPI.pageList(queryParams.value).then((res) => {
    tableList.value = res.records
    total.value = res.total
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

function add() {
  formData.value = { enabled: true }
  title.value = '新增'
  dialogVisible.value = true
}

function edit(row: typeof state.formData) {
  formData.value = { ...row }
  title.value = '编辑'
  dialogVisible.value = true
}

const formRef = useTemplateRef('formRef')
function submit() {
  formRef.value.validate().then(() => {
    submitting.value = true
    const userId = formData.value.id
    const fn = userId ? UserAPI.update : UserAPI.add
    fn(formData.value).then(() => {
      ElMessage.success('操作成功！')
      dialogVisible.value = false
      handleQuery()
    }).finally(() => {
      submitting.value = false
    })
  })
}

function handleDelete(userId: number) {
  ElMessageBox.confirm('确认删除已选中的数据项?', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    loading.value = true
    UserAPI.delete(userId)
      .then(() => {
        ElMessage.success('操作成功！')
        handleQuery()
      })
      .finally(() => {
        loading.value = false
      })
  },
  )
}

const rendered = ref(false)
nextTick(() => {
  rendered.value = true
})
</script>

<template>
  <div class="flex flex-col gap-$gap pb-56">
    <section class="bg-bg rounded-4" b="~ border-lighter">
      <header class="font-bold leading-extra-large" p="x-24 y-16" b="b border" text="20 textPrimary">表单1</header>
      <el-form class="px-24 py-16" label-position="top">
        <div class="flex gap-80">
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form1Data.name1" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form1Data.name2" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="这里是日期" class="flex-1">
            <el-date-picker v-model="form1Data.date" class="!w-full" />
          </el-form-item>
          <el-form-item label="这里是日期区间" class="flex-1">
            <el-date-picker v-model="form1Data.dateRange" type="daterange" class="!w-full" />
          </el-form-item>
        </div>

        <div class="flex gap-80 mt-10">
          <el-form-item label="网站" class="flex-1">
            <el-input v-model="form1Data.website1" placeholder="请输入">
              <template #prepend>Http://</template>
              <template #append>.com</template>
            </el-input>
          </el-form-item>
          <el-form-item label="网站" class="flex-1">
            <el-input v-model="form1Data.website2" placeholder="请输入">
              <template #prepend>Http://</template>
              <template #append>.com</template>
            </el-input>
          </el-form-item>
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form1Data.name3" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form1Data.name4" placeholder="请输入" />
          </el-form-item>
        </div>
      </el-form>
    </section>

    <section class="bg-bg rounded-4" b="~ border-lighter">
      <header class="font-bold leading-extra-large" p="x-24 y-16" b="b border" text="20 textPrimary">表单2</header>
      <el-form class="px-24 py-16" label-position="top">
        <div class="flex gap-80">
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form2Data.name1" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form2Data.name2" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="这里是日期" class="flex-1">
            <el-date-picker v-model="form2Data.date" class="!w-full" />
          </el-form-item>
          <el-form-item label="这里是日期区间" class="flex-1">
            <el-date-picker v-model="form2Data.dateRange" type="daterange" class="!w-full" />
          </el-form-item>
        </div>

        <div class="flex gap-80 mt-10">
          <el-form-item label="网站" class="flex-1">
            <el-input v-model="form2Data.website1" placeholder="请输入">
              <template #prepend>Http://</template>
              <template #append>.com</template>
            </el-input>
          </el-form-item>
          <el-form-item label="网站" class="flex-1">
            <el-input v-model="form2Data.website2" placeholder="请输入">
              <template #prepend>Http://</template>
              <template #append>.com</template>
            </el-input>
          </el-form-item>
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form2Data.name3" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="这里是名称" class="flex-1">
            <el-input v-model="form2Data.name4" placeholder="请输入" />
          </el-form-item>
        </div>
      </el-form>
    </section>

    <section class="bg-bg rounded-4" b="~ border-lighter">
      <header class="font-bold leading-extra-large" p="x-24 y-16" b="b border" text="20 textPrimary">表单3</header>
      <div class="px-20 pt-10 pb-40">
        <el-table stripe :data="tableList" highlight-current-row>
          <el-table-column prop="nickname" label="姓名" min-width="120" show-overflow-tooltip />
          <el-table-column prop="username" label="账号" min-width="120" show-overflow-tooltip />
          <el-table-column prop="phone" label="手机号" min-width="140" show-overflow-tooltip />
          <el-table-column prop="roleNames" label="角色" min-width="120" show-overflow-tooltip />
          <el-table-column prop="modifyBy" label="更新人" min-width="120" show-overflow-tooltip />
          <el-table-column prop="modifyTime" label="更新时间" min-width="160" show-overflow-tooltip />
          <el-table-column fixed="right" label="操作" min-width="160">
            <template #default="scope">
              <el-link type="primary" underline="never" @click="edit(scope.row)">编辑</el-link>
              <el-link class="mx-$Space-Default-12" type="danger" underline="never" @click="handleDelete(scope.row.id)">删除</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-button class="w-full mt-14" :icon="Plus" @click="add">新增数据</el-button>
      </div>

      <el-dialog v-model="dialogVisible" destroy-on-close width="40vw" :title="title" append-to-body :close-on-click-modal="false">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="4.3vw" class="p-30" size="large">
          <el-form-item label="账号" prop="username">
            <el-input v-model="formData.username" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="姓名" prop="nickName">
            <el-input v-model="formData.nickname" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="手机号" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="角色" prop="roleName">
            <el-input v-model="formData.roleName" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="状态" prop="enabled">
            <el-radio-group v-model="formData.enabled">
              <el-radio :value="true">启用</el-radio>
              <el-radio :value="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <template #footer>
          <div>
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button :loading="submitting" type="primary" @click="submit">确认</el-button>
          </div>
        </template>
      </el-dialog>
    </section>

    <!--    底部确认功能 -->
    <teleport v-if="rendered" to=".app-container-out">
      <footer class="confirmBar bg-bg flex justify-end absolute z-99" inset="b-0 x-0" p="y-12 x-32">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button :loading="submitting" type="primary" @click="submit">确认</el-button>
      </footer>
    </teleport>
  </div>
</template>

<style scoped lang="scss">
.confirmBar {
  border-top: 1px solid var(--el-border-color-lighter);
  box-shadow: 0 0 12px 0 var(--Shadow-12, rgba(0, 0, 0, 0.12));
}
</style>
