<script setup lang="ts">
import type { DeviceVO } from '@/api/monitor.ts'
import { MonitorAPI } from '@/api/monitor.ts'
import ControlPanel from './components/ControlPanel.vue'
import MonitorInfo from './components/MonitorInfo.vue'
import Playback from './components/Playback.vue'

const route = useRoute()
const id = route.params.id as string
const router = useRouter()
if (!id) {
  router.push('/list/monitor')
}

const data = ref<DeviceVO>({})
const loading = ref(false)
function handleQuery() {
  loading.value = true
  MonitorAPI.detail(id).then((res) => {
    data.value = res
    if (res.misc) {
      const { deviceId, channelId } = res.misc
      queryMonitor(deviceId, channelId)
    }
  }).finally(() => {
    loading.value = false
  })
}

id && handleQuery()

const videoUrl = ref()
const monitorLoading = ref(false)
function queryMonitor(deviceId: string, channelId: string) {
  monitorLoading.value = true
  MonitorAPI.play(deviceId, channelId)
    .then((res) => {
      videoUrl.value = res.wss_flv
    })
    .finally(() => {
      monitorLoading.value = false
    })
}

const showPlayer = ref(true)
function refresh() {
  showPlayer.value = false
  nextTick(() => {
    showPlayer.value = true
  })
}
</script>

<template>
  <div v-loading="loading" class="flex flex-col flex-gap gap-$gap">
    <section class="bg-bg rounded-4" b="~ border-lighter">
      <header class="flex justify-between leading-extra-large" p="x-24 y-16">
        <aside class="flex items-center">
          <span class="font-bold" text="extra-large">监控详情</span>
          <div class="w-1 h-12 bg-border-lighter mx-12" />
          <span text="14 text-secondary">{{ data.misc?.channelName }}</span>
          <el-tag v-if="data.online" type="success" size="small" class="ml-12">在线</el-tag>
          <el-tag v-else type="danger" size="small" class="ml-12">离线</el-tag>
        </aside>
        <aside>
          <el-button @click="refresh">刷新</el-button>
          <el-button>设置</el-button>
          <el-button type="danger">停止录制</el-button>
        </aside>
      </header>
    </section>
    <section class="grow flex gap-$gap">
      <div class="grow">
        <Player v-if="data.misc && showPlayer" class="h-full" :videoUrl="videoUrl" :loading="monitorLoading" />
      </div>
      <el-tabs class="w-318 bg-bg rounded-r-base" stretch type="border-card">
        <el-tab-pane label="控制台">
          <ControlPanel :data="data" />
        </el-tab-pane>
        <el-tab-pane label="录像信息" lazy>
          <MonitorInfo />
        </el-tab-pane>
        <el-tab-pane label="录像回放" lazy>
          <Playback v-if="data.misc" :channelId="data.misc.channelId" :deviceId="data.misc.deviceId" />
        </el-tab-pane>
      </el-tabs>
    </section>
  </div>
</template>

<style scoped lang="scss">
</style>
