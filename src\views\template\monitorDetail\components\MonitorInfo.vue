<script setup lang="ts">
interface InfoItem {
  label: string
  value: string | number
}

interface InfoModule {
  title: string
  icon?: string // 图标占位，后续手动替换
  rows: InfoItem[][]
}

const modules: InfoModule[] = [
  {
    title: '基本信息',
    icon: 'basic-info', // 占位符
    rows: [
      [
        { label: '设备编号', value: 'CAM-001' },
        { label: '设备名称', value: '大门入口' },
      ],
      [
        { label: '录制时间', value: '2h 14m 35s' },
        { label: '文件大小', value: '1.2 GB' },
      ],
      [
        { label: '录制时间段', value: '2025-12-12 14:30:45 - 15:45:20' },
      ],
    ],
  },
  {
    title: '技术参数',
    icon: 'technical-params', // 占位符
    rows: [
      [
        { label: '分辨率', value: '1920x1080' },
        { label: '帧率', value: '25 fps' },
      ],
      [
        { label: '码率', value: '2.5 Mbps' },
        { label: '编码格式', value: 'H.264' },
      ],
    ],
  },
  {
    title: '位置信息',
    icon: 'location-info', // 占位符
    rows: [
      [
        { label: '安装位置', value: '四川省成都市金牛区茶店河路2980号-1号楼主入口' },
      ],
      [
        { label: 'IP地址', value: '*************' },
        { label: 'MAC地址', value: '00:12:34:56:78:9A' },
      ],
    ],
  },
]
</script>

<template>
  <el-scrollbar height="calc(100vh - 15vw)">
    <div class="flex flex-col gap-20 leading-base">
      <!-- 信息模块 -->
      <div
        v-for="(module, moduleIndex) in modules"
        :key="moduleIndex"
        class="flex flex-col gap-16"
      >
        <!-- 模块标题 -->
        <div class="flex items-center gap-5">
          <!-- 图标占位符，稍后手动替换 -->
          <div :class="`i-temp-${module.icon}`" text="16 primary" />
          <span font="500">{{ module.title }}</span>
        </div>

        <!-- 模块内容 -->
        <div class="flex flex-col gap-10">
          <!-- 每行信息 -->
          <div
            v-for="(row, rowIndex) in module.rows"
            :key="rowIndex"
            class="flex gap-10"
          >
            <!-- 信息卡片 -->
            <div
              v-for="(item, itemIndex) in row"
              :key="itemIndex"
              class="flex-1 min-w-0 bg-$el-fill-color-light rounded-4 px-16 py-12"
            >
              <div class="flex flex-col gap-4">
                <div text="text-secondary">
                  {{ item.label }}
                </div>
                <div>
                  {{ item.value }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<style scoped lang="scss">
</style>
