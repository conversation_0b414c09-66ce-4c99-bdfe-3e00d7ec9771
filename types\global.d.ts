declare global {
  /**
   * 分页查询参数
   */
  type PageQuery<T> = Partial<T> & {
    pageNum?: number
    pageSize?: number
    keywords?: string
  }

  type PromiseList<T> = Promise<Result<T[]>>

  type PromiseResult<T> = Promise<Result<T>>

  type PromiseRecords<T> = Promise<Result<Records<T>>>

  interface Result<T> {
    code: number
    data: T
    msg: string
  }

  interface Records<T> {
    records: T[]
    total: number
    size: number
    current: number
    orders: any[]
    optimizeCountSql: boolean
    searchCount: boolean
    maxLimit?: any
    countId?: any
    pages: number
  }
}
