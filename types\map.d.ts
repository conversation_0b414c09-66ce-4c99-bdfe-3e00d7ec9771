export interface Pois {
  parent: string
  address: string
  distance: string
  pcode: string
  adcode: string
  pname: string
  cityname: string
  type: string
  typecode: string
  adname: string
  citycode: string
  name: string
  location: string
  id: string
}

export interface AddrSearchInfo {
  count: string
  infocode: string
  pois: Pois[]
  status: string
  info: string
}

export interface LocateInfo {
  result: LocateInfoResult
  msg: string
  status: string
}
export interface LocateInfoResultLocation {
  lon: number
  lat: number
}
export interface LocateInfoResultAddressComponent {
  address: string
  town: string
  nation: string
  city: string
  county_code: string
  poi_position: string
  county: string
  city_code: string
  address_position: string
  poi: string
  province_code: string
  town_code: string
  province: string
  road: string
  road_distance: number
  address_distance: number
  poi_distance: number
}
export interface LocateInfoResult {
  formatted_address: string
  location: LocateInfoResultLocation
  addressComponent: LocateInfoResultAddressComponent
}

export interface TianPois {
  eaddress: string
  address: string
  city: string
  provinceCode: string
  cityCode: string
  county: string
  typeName: string
  source: string
  typeCode: string
  lonlat: string
  countyCode: string
  ename: string
  province: string
  phone: string
  poiType: string
  name: string
  hotPointID: string
  lat: number
  lng: number
  fullAddress?: string
}

export interface Admins {
  adminName: string
  adminCode: number
}

export interface Prompt {
  type: number
  admins: Admins[]
}

export interface Status {
  cndesc: string
  infocode: number
}

export interface TianDiTuAddrInfo {
  area: any
  count: string
  resultType: number
  pois: TianPois[]
  prompt: Prompt[]
  status: Status
  keyWord: string
}
