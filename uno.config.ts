import presetRemToPx from '@unocss/preset-rem-to-px'
import { presetAttributify, presetIcons, presetWind3, transformerDirectives, transformerVariantGroup } from 'unocss'
import Unocss from 'unocss/vite'
import { generateCollections, generateSafeList, getIconDirectories } from './vitePlugin/utils'

// 本地 SVG 图标存放目录
const iconsDir = './src/icons'

export function unocssPlugin() {
  // 只执行一次目录读取，避免重复执行
  const directories = getIconDirectories(iconsDir)

  return Unocss({
    presets: [
      presetAttributify({
        /* preset options */
      }),
      presetWind3(),
      presetIcons({
        extraProperties: {
          'width': '1em',
          'height': '1em',
          'display': 'inline-block',
          'flex-shrink': '0',
        },
        collections: generateCollections(directories),
      }),
      presetRemToPx({ baseFontSize: 4 }),
    ],
    safelist: generateSafeList(directories),
    transformers: [transformerVariantGroup(), transformerDirectives()],
    theme: {
      colors: {
        bg: 'var(--el-bg-color)',
        primary: 'var(--el-color-primary)',
        success: 'var(--el-color-success)',
        warning: 'var(--el-color-warning)',
        danger: 'var(--el-color-danger)',

        text: {
          primary: 'var(--el-text-color-primary)',
          regular: 'var(--el-text-color-regular)',
          secondary: 'var(--el-text-color-secondary)',
          placeholder: 'var(--el-text-color-placeholder)',
          disabled: 'var(--el-text-color-disabled)',
          DEFAULT: 'var(--el-text-color-primary)',
        },

        border: {
          light: 'var(--el-border-color-light)',
          lighter: 'var(--el-border-color-lighter)',
          DEFAULT: 'var(--el-border-color)',
        },
      },

      fontSize: {
        'extra-small': 'var(--el-font-size-extra-small)',
        'small': 'var(--el-font-size-small)',
        'base': 'var(--el-font-size-base)',
        'medium': 'var(--el-font-size-medium)',
        'large': 'var(--el-font-size-large)',
        'extra-large': 'var(--el-font-size-extra-large)',
      },

      lineHeight: {
        'extra-small': 'var(--el-font-line-height-extra-small)',
        'small': 'var(--el-font-line-height-small)',
        'base': 'var(--el-font-line-height-base)',
        'medium': 'var(--el-font-line-height-medium)',
        'large': 'var(--el-font-line-height-large)',
        'extra-large': 'var(--el-font-line-height-extra-large)',
      },

      borderRadius: {
        small: 'var(--el-border-radius-small)',
        base: 'var(--el-border-radius-base)',
        round: 'var(--el-border-radius-round)',
      },
    },
    extendTheme: (theme) => {
      // 查看 theme 结构，便于调试
      // console.log(theme)
    },

  })
}
