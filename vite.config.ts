/// <reference types="vitest" />
import path from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import { createBuild } from './vitePlugin/build'
import { createVitePlugins } from './vitePlugin/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isDev = mode === 'development'

  const env = loadEnv(mode, process.cwd())

  return {
    test: {
      globals: true,
      environment: 'jsdom',
    },
    resolve: {
      alias: {
        '@': path.resolve(import.meta.dirname, 'src'),
        '#': path.resolve(import.meta.dirname, 'types'),
      },
    },
    esbuild: {
      pure: ['console.log', 'console.count'],
    },
    plugins: createVitePlugins(),

    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },

    // 构建配置
    build: createBuild(),

    server: {
      host: '0.0.0.0',
      hmr: true,
      port: 9527,
      open: false,
      proxy: {
        '/api/v1': {
          // target: 'https://whzl.vankeytech.com:9905', // 测试环境
          target: 'https://mgt.scwhnk.com/', // 正式环境
          changeOrigin: true,
        },
      },
    },
  }
})
