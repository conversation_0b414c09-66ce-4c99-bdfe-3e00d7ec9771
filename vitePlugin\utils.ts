import fs from 'node:fs'
import path from 'node:path'
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'

// 递归读取目录并返回所有目录信息
export function getIconDirectories(dir: string, basePath: string = dir): Array<{
  path: string
  name: string
  files: string[]
}> {
  const result: Array<{ path: string, name: string, files: string[] }> = []

  try {
    const items = fs.readdirSync(dir)
    const svgFiles = items.filter((item) => {
      const itemPath = path.join(dir, item)
      const stats = fs.statSync(itemPath)
      return stats.isFile() && item.endsWith('.svg')
    })

    // 添加当前目录信息
    const dirName = dir === basePath ? 'icon' : path.basename(dir)
    result.push({
      path: dir,
      name: dirName,
      files: svgFiles,
    })

    // 递归处理子目录
    const subDirs = items.filter((item) => {
      const itemPath = path.join(dir, item)
      const stats = fs.statSync(itemPath)
      return stats.isDirectory()
    })

    for (const subDir of subDirs) {
      const subDirPath = path.join(dir, subDir)
      result.push(...getIconDirectories(subDirPath, basePath))
    }
  }
  catch (error) {
    console.error(`无法读取图标目录 ${dir}:`, error)
  }

  return result
}

// 生成 collections 配置
export function generateCollections(directories: Array<{ path: string, name: string, files: string[] }>) {
  const collections: Record<string, any> = {}

  for (const { path: dirPath, name } of directories) {
    collections[name] = FileSystemIconLoader(dirPath, (svg) => {
      // 如果 SVG 文件未定义 `fill` 属性，则默认填充 `currentColor`
      // 这样图标颜色会继承文本颜色，方便在不同场景下适配
      return svg.includes('fill="')
        ? svg
        : svg.replace(/^<svg /, '<svg fill="currentColor" ')
    })
  }

  return collections
}

// 生成 safelist
export function generateSafeList(directories: Array<{ path: string, name: string, files: string[] }>) {
  const safelist: string[] = []

  for (const { name, files } of directories) {
    for (const file of files) {
      const iconName = file.replace('.svg', '')
      safelist.push(`i-${name}-${iconName}`)
    }
  }

  return safelist
}
